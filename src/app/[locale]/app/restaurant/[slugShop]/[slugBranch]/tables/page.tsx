'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';

// Mock reservation data
const mockReservations = [
  {
    id: 'res1',
    time: '7:00 PM',
    customerName: '<PERSON>',
    partySize: 4,
    table: 'Table 3',
    status: 'Confirmed',
    date: '2024-01-15',
    phone: '+****************',
    email: '<EMAIL>',
    notes: 'Birthday celebration'
  },
  {
    id: 'res2',
    time: '7:30 PM',
    customerName: '<PERSON>',
    partySize: 2,
    table: 'Table 7',
    status: 'Confirmed',
    date: '2024-01-15',
    phone: '+****************',
    email: 'ethan.ben<PERSON>@email.com',
    notes: 'Anniversary dinner'
  },
  {
    id: 'res3',
    time: '8:00 PM',
    customerName: '<PERSON>',
    partySize: 6,
    table: 'Table 11',
    status: 'Pending',
    date: '2024-01-15',
    phone: '+****************',
    email: '<EMAIL>',
    notes: 'Business meeting'
  },
  {
    id: 'res4',
    time: '8:30 PM',
    customerName: 'Liam Harper',
    partySize: 3,
    table: 'Table 2',
    status: 'Confirmed',
    date: '2024-01-15',
    phone: '+****************',
    email: '<EMAIL>',
    notes: ''
  },
  {
    id: 'res5',
    time: '9:00 PM',
    customerName: 'Ava Foster',
    partySize: 5,
    table: 'Table 9',
    status: 'Confirmed',
    date: '2024-01-15',
    phone: '+****************',
    email: '<EMAIL>',
    notes: 'Family dinner'
  }
];

// Mock table data with images
const mockTables = [
  {
    id: '1',
    number: 1,
    name: 'Table 1',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBitRJJIAwrsT3sN69YFrCfepOrR3pneJ4inWpwR7iu2tNMseZE90YE_oCNgkmq5Gs_UyUyocZX_daSu7NymBhRzfJNJRLDw3DRHhJ3HgXSSPZMyBTglltX4eAZaQ3guLhNiLPkYbwwmj77cLZogMsBiBm-6X4wKpxb-20L1PlcjmWmV_OTwZFlsVy_Q-52Bw-nvA7xF1H6egrCjRuyBkQz_H9jh_Ooe-9XgXYdUb8yvdgkJPgPXxmjpmHDFdx4aHichBxyNhdEo8WW',
    location: 'dining'
  },
  {
    id: '2',
    number: 2,
    name: 'Table 2',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCm5kKI8V-CR8Cq_8PQH4XUAiPQirzRWlhKic0WW1RETxcRK-_Ar3CJjWXGUmL7zwdy0fhyEb6uKSin4etsZO72RQ-9anPe5RnTyJSuMSPntdFdejZEVZCTQ_of2HgVS4wiqf8wkmwzT7PbrONDjcdli0eXZURLYp0iRgXrSndH8i-wLDieR9ZonyQMrMiOd0zVNzsV1Lf0lHlTAcamZTZmhz66EcDm0Y-LT-gU29Liy1qhDb3ykMOCMrobBocxOy1uLa8lhYkp8qSj',
    location: 'dining'
  },
  {
    id: '3',
    number: 3,
    name: 'Table 3',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCmluEqeoS8maaJInF5ENft68j3Zo6lrjuN6Cz7EX7tmiurjpgiPC9qFmQ7ouTL5CYHEClO4_K_Gm8g1Ez2N5hvb-EnIEFj6yrbX9nR6oPzI7jpKCsiZ4uhOV0-0s_7FYdmG_jOntuziVZiVzaA7k34MrhfEHBiKeCWcrS_eGHx_0hIdpIeWlTAgRvihrqcs4H0aBkPRqRnQ-JoHezoQJkBq1MANw1b93TUOfADf3916wSitfLZ5S0LOvSeK3SOJBSoTJc8gV0LiQOV',
    location: 'dining'
  },
  {
    id: '4',
    number: 4,
    name: 'Table 4',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDeKZLfgmskuVwyKS6M2mqm-wHKoVsx8DUwJ6orJ3HRCmqVzBBNb18hK6B3-rHkTmyFU9u8I67VNnvnp8zp-7hjz2q2sw57FOn4Xg9fWvMpmBSfcyogsig7XCGMWkWIsSw9QkY275_LLly2gW72XfWBGIOXvA9IYAg3lhMMczeNRmF5CKp7-kNIRydPwoUlRyFJzDLnsznrY0rgP3JsjxN775-duLZB5pHV26d8CEz-9Kybxe25OBNE9Lr--B8ghsYf4AYEgSA1NpoX',
    location: 'dining'
  },
  {
    id: '5',
    number: 5,
    name: 'Table 5',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBaYcUah5GnpaX-LjIAnsnx7b_FGm530dKhaX9qcIR5xbMeZb5Jb4PTg2Mqiz5mheEjIXkdPnwWHbYeoeyRYeKxxhlxkQo9iflGzKG4oD2YuzuONa-DMQBc4F9iDEfO7F_AqIZ6Hb4nyc7J7ATNBgcrfduzhToQ5uH7YMzcAe-yNk7K5TiQAJxiSJY_EhzZTlliY7ThA4olnMU74LqlKQ9Q4lKXSgUdtLGqwf5n05eG84-4F4qcmFrkvwv-SPac7NG4U6KgcIkdXR7U',
    location: 'dining'
  },
  {
    id: '6',
    number: 6,
    name: 'Table 6',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuC3UPgd7rhRsDT7eYfAD2T-sPufNEqRrHt5F6dHFgUXuNokqidPvPIdhj3XI4faNbEaoQlvdtuFn-U7t4dkbq_RrUyNsUkfgNJKCNFnixLjqvF5_7YNHuqbYpeBlzoY6EK4p9pyznahnSoHr8XMpikZC6_g1xVmvHna5tEadf1ov8--gq51t7rzp6KSCrCsgdx54NAfSyPmCmhNYUS0OAmH5zuk7wG3Y6OAKzF-Hukya2ehV8z7_1fMbUH4yNw7dugwyru9is9ljIPo',
    location: 'dining'
  },
  {
    id: '7',
    number: 7,
    name: 'Table 7',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuD9HQ9qBH6E7N3BPxRfx1bJXYd-TZZ_6SLmdly5C6k5wNtgkYmpZG06l39IEz_Ia2tiCQi5BdrFigYqg8Gpq1-xI1WgDzGmg0Qat3AfwRURE5qm-65to1Zi0EgrXtBdCYGw5g1s3l2NWWfQer0oRvEIDKXnmBOeO_CecYUgeei8goDhIbiUOuPqv-UNzHQ1B2o_WHSFIi-SHIgK71I9qBSRNkJprUpW8Xk75UNFiMhj8zZT8o1YWmVw-yeT7RkO3KnNzBXDBfMrjPGk',
    location: 'dining'
  },
  {
    id: '8',
    number: 8,
    name: 'Table 8',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAbEbLgXmmGsSE-rc6BgB_EHvxJYYfk_q1Q6LdxMjBBZusJOu-SR5Lj9U-j1thggcac0GlBkzjriHuqAJT8rU1zn0t6rhOecLtktvA-gqb__BzGsL4DPIDmo66KCH7G_kt8dpUhYOYQhCgBJVcpHR2bqnsGYutNIJydMCAbtVyRHMFMhaMmLqAnywhsFVUeh1wyjBNKXhw8DSNRkWVYCF3b_zUoQhGV-Y1xeEwwJ3uJGuTJanIbsc8W4lr-_fMslYcr_s7uWXg6_jRB',
    location: 'dining'
  },
  {
    id: '9',
    number: 9,
    name: 'Table 9',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCIz0HXWiDXpcBAoN5tkRxMfmuAZEr_bR5i1v-LJGz3rJ3bvTCxwd8-zdvsbOZZ846ZRH69lPKTQiW5NhJbP_-e3T_xnLqjmYDVrm43IqtOt6vDblBqxeC5_gYUT2gwS-pdLP3TnRxdGJIHEbW3ExL4uUJW4Em8SGOCPccBfy_FSmLzYZDwWJ_GojPr5zBp0JbwDDs2nR2aN2eiI385VFdl4nezOKcrRdmm1PrhMYjFoOHAE5eczLj91yOzHwZbmIgJJK7l5xSXD7eC',
    location: 'dining'
  },
  {
    id: '10',
    number: 10,
    name: 'Table 10',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCBOGlr9W9KBrS6VSv8V5HCE6ylasPrD1NvE3mMs9AB2hhRc-2lZbrQivuaDLwLoe2I9mGFwJ1LFXVof7D8u-eu_9l3XGCfh4y2Sbi4OA-hdCU45bq0R7yRdaavSiIuVcseKMzxbcJ8q6Q482YXBk2DqSLE0Zi34Ylbbdc-mhH0QwXgIQXlS55M3aporIXMXJQ_-87FmDSUwDZ2jB04dihpgs-zOGDxvIj9qWKdGNNzBkvgy3TjFzGDAExf_Dic6ZUJQdnAAvcxTc6j',
    location: 'outdoor'
  },
  {
    id: '11',
    number: 11,
    name: 'Table 11',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDQKknOb2zf7QS-q5HVjzt6C9rVLYuiEImsgWF3cHQ1DIe3pwEDkS52hck_SgugsNk5kbogoXGY-MZpIZF2dGDZzE2m701-wJWBM6U-uZbGAKdxUsd2hSKZ1pSk195CtMz850v40dZ0z5TPpuOQVWfwrE4RkYji0Jaf7G6cxE02JCooay55mNHT28dHhBT0NisuKVZ0lDnd7juDMYSTttW2hEYNGt7yffWfjwZYeVVyEJlyH2XhBE8p90m1aftxmiNF5FKyOazJyKRc',
    location: 'outdoor'
  },
  {
    id: '12',
    number: 12,
    name: 'Table 12',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBPOpsvPGLf0WjPng7RmEMaYXivOfOM2lai3svFLXM04vqptsNAX9uxiWvS-o_NEOdNNIaKb2DKm75VHtfI3r9zTGyXzgXxEzXohtWdRDak4gLqLk548iHHZqPMFKqTLTxVXcmc6uNgV2-y9RuxQJn4pIMflICpykz44_m7-cTlT-EgieVnPwgCOTLvyUxfsrckXlbi8Cdl_ycO--899rs5YXfncTlBEC3GEt5KDUCrjJyx4VJRnjWgwjwRvs6bqPmazyRTqvKIT8QB',
    location: 'outdoor'
  },
  {
    id: '13',
    number: 13,
    name: 'Table 13',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBYiexir2spyoRDU3_rkmQBrbjy6urJ2_sWfYE0qreHXkmTuT2tkefw9kzRmZrjD69ySwjLf33iP_H5ZqCiRDlsWX2GCYE1ISYFZ1nBl6h56DaWb_wIQJ86ClIsL5xr9IpJ45vkG4Vn8OYfXx86M2zs-R7sc-PcWx62h0HTIQYOJCTvSP9TcYFb-Ap1ASfJ98acBSq-TlqXKrIquDlTdbkkATyF_Qy71BzuRznevhEI4VrBPYzJn_HjNdIj9bPg53WJI5c78GMn86-e',
    location: 'outdoor'
  }
];

interface TablesPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function TablesPage({ params }: TablesPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('floor-plan');

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="text-center py-12">
        <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
        <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
      </div>
    );
  }

  // Filter tables by location
  const diningTables = mockTables.filter(table => table.location === 'dining');
  const outdoorTables = mockTables.filter(table => table.location === 'outdoor');

  return (
    <>
      {/* Page Header */}
      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] tracking-light text-[32px] font-bold leading-tight">Table Layout</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">Manage your restaurant's table layout and reservations.</p>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <TabsList className="grid w-full grid-cols-3 bg-transparent border-b border-[#e2dcd4] rounded-none h-auto p-0">
          <TabsTrigger
            value="floor-plan"
            className="border-b-2 border-transparent data-[state=active]:border-[#e5ccb2] data-[state=active]:bg-transparent bg-transparent rounded-none pb-3 pt-4 text-[#8a745c] data-[state=active]:text-[#181510] font-bold text-sm"
          >
            Floor Plan
          </TabsTrigger>
          <TabsTrigger
            value="reservations"
            className="border-b-2 border-transparent data-[state=active]:border-[#e5ccb2] data-[state=active]:bg-transparent bg-transparent rounded-none pb-3 pt-4 text-[#8a745c] data-[state=active]:text-[#181510] font-bold text-sm"
          >
            Reservations
          </TabsTrigger>
          <TabsTrigger
            value="waitlist"
            className="border-b-2 border-transparent data-[state=active]:border-[#e5ccb2] data-[state=active]:bg-transparent bg-transparent rounded-none pb-3 pt-4 text-[#8a745c] data-[state=active]:text-[#181510] font-bold text-sm"
          >
            Waitlist
          </TabsTrigger>
        </TabsList>

        <TabsContent value="floor-plan" className="mt-6">
          {/* Dining Area */}
          <h2 className="text-[#181510] text-[22px] font-bold leading-tight tracking-[-0.015em] pb-3 pt-5">Dining Area</h2>
          <div className="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 mb-8">
            {diningTables.map((table) => (
              <Link key={table.id} href={`/app/restaurant/${slugShop}/${slugBranch}/tables/${table.id}`}>
                <div
                  className="bg-cover bg-center flex flex-col gap-3 rounded-lg justify-end p-4 aspect-square cursor-pointer hover:opacity-80 transition-opacity"
                  style={{
                    backgroundImage: `linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0) 100%, rgba(0, 0, 0, 0.1) 100%, rgba(0, 0, 0, 0) 100%), url("${table.image}")`
                  }}
                >
                  <p className="text-white text-base font-bold leading-tight w-4/5 line-clamp-2">{table.name}</p>
                </div>
              </Link>
            ))}
          </div>

          {/* Outdoor Patio */}
          <h2 className="text-[#181510] text-[22px] font-bold leading-tight tracking-[-0.015em] pb-3 pt-5">Outdoor Patio</h2>
          <div className="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 mb-8">
            {outdoorTables.map((table) => (
              <Link key={table.id} href={`/app/restaurant/${slugShop}/${slugBranch}/tables/${table.id}`}>
                <div
                  className="bg-cover bg-center flex flex-col gap-3 rounded-lg justify-end p-4 aspect-square cursor-pointer hover:opacity-80 transition-opacity"
                  style={{
                    backgroundImage: `linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0) 100%, rgba(0, 0, 0, 0.1) 100%, rgba(0, 0, 0, 0) 100%), url("${table.image}")`
                  }}
                >
                  <p className="text-white text-base font-bold leading-tight w-4/5 line-clamp-2">{table.name}</p>
                </div>
              </Link>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 mt-6">
            <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables/layout-editor`}>
              <button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#f1edea] text-[#181510] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#e2dcd4] transition-colors">
                <span className="truncate">Edit Layout</span>
              </button>
            </Link>
            <Link href={`/app/restaurant/${slugShop}/${slugBranch}/reservations/new`}>
              <button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#e5ccb2] text-[#181510] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#d6bd9e] transition-colors">
                <span className="truncate">Add Reservation</span>
              </button>
            </Link>
          </div>
        </TabsContent>

        <TabsContent value="reservations" className="mt-6">
          <h2 className="text-[#181511] text-[22px] font-bold leading-tight tracking-[-0.015em] pb-3 pt-5">Upcoming Reservations</h2>
          <div className="py-3 @container">
            <div className="flex overflow-hidden rounded-xl border border-[#e5e1dc] bg-white">
              <table className="flex-1">
                <thead>
                  <tr className="bg-white">
                    <th className="table-reservations-column-120 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">Time</th>
                    <th className="table-reservations-column-240 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">Name</th>
                    <th className="table-reservations-column-360 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">Party Size</th>
                    <th className="table-reservations-column-480 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">Table</th>
                    <th className="table-reservations-column-600 px-4 py-3 text-left text-[#181511] w-60 text-sm font-medium leading-normal">Status</th>
                    <th className="table-reservations-column-720 px-4 py-3 text-left w-60 text-[#887663] text-sm font-medium leading-normal">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {mockReservations.map((reservation) => (
                    <tr key={reservation.id} className="border-t border-t-[#e5e1dc]">
                      <td className="table-reservations-column-120 h-[72px] px-4 py-2 w-[400px] text-[#887663] text-sm font-normal leading-normal">
                        {reservation.time}
                      </td>
                      <td className="table-reservations-column-240 h-[72px] px-4 py-2 w-[400px] text-[#181511] text-sm font-normal leading-normal">
                        {reservation.customerName}
                      </td>
                      <td className="table-reservations-column-360 h-[72px] px-4 py-2 w-[400px] text-[#887663] text-sm font-normal leading-normal">
                        {reservation.partySize}
                      </td>
                      <td className="table-reservations-column-480 h-[72px] px-4 py-2 w-[400px] text-[#887663] text-sm font-normal leading-normal">
                        {reservation.table}
                      </td>
                      <td className="table-reservations-column-600 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          className={`flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 text-sm font-medium leading-normal w-full ${
                            reservation.status === 'Confirmed'
                              ? 'bg-[#f4f2f0] text-[#181511]'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          <span className="truncate">{reservation.status}</span>
                        </button>
                      </td>
                      <td className="table-reservations-column-720 h-[72px] px-4 py-2 w-60 text-[#887663] text-sm font-bold leading-normal tracking-[0.015em] cursor-pointer hover:text-[#181511]">
                        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/reservations/${reservation.id}`}>
                          View
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <style dangerouslySetInnerHTML={{
              __html: `
                @container(max-width:120px){.table-reservations-column-120{display: none;}}
                @container(max-width:240px){.table-reservations-column-240{display: none;}}
                @container(max-width:360px){.table-reservations-column-360{display: none;}}
                @container(max-width:480px){.table-reservations-column-480{display: none;}}
                @container(max-width:600px){.table-reservations-column-600{display: none;}}
                @container(max-width:720px){.table-reservations-column-720{display: none;}}
              `
            }} />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 mt-6">
            <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables/layout-editor`}>
              <button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#f4f2f0] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#e2dcd4] transition-colors">
                <span className="truncate">Edit Layout</span>
              </button>
            </Link>
            <Link href={`/app/restaurant/${slugShop}/${slugBranch}/reservations/new`}>
              <button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e58219] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#d4741a] transition-colors">
                <span className="truncate">Add Reservation</span>
              </button>
            </Link>
          </div>
        </TabsContent>

        <TabsContent value="waitlist" className="mt-6">
          <div className="p-8 text-center text-[#8a745c]">
            <p>Waitlist feature coming soon</p>
          </div>
        </TabsContent>
      </Tabs>
    </>
  );
}
