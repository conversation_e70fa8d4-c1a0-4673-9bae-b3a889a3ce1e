'use client';

import React from 'react';
import { use } from 'react';
import { Link } from '@/i18n/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  ArrowLeft,
  Settings,
  MapPin,
  Phone,
  Mail,
  Globe,
  Users,
  Calendar,
  TrendingUp,
  Store,
  Plus,
  ArrowRight
} from 'lucide-react';
import { MESSAGES } from '@/lib/constants/messages';
import { useRestaurant } from '@/hooks/useRestaurant';
import { AppLoading } from '@/components/ui/app-loading';

interface RestaurantPageProps {
  params: Promise<{
    slugShop: string;
  }>;
}

export default function RestaurantPage({ params }: RestaurantPageProps) {
  const { slugShop } = use(params);

  // In a real app, we would fetch restaurant by slug
  // For now, we'll use mock data
  const { isLoading } = useRestaurant(slugShop);

  // Mock data for demonstration
  const mockRestaurant = {
    id: '1',
    name: 'Bella Vista Restaurant',
    slug: slugShop,
    description: 'Authentic Italian cuisine with a modern twist, featuring fresh ingredients and traditional recipes passed down through generations.',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Main Street, Downtown',
    city: 'New York',
    country: 'United States',
    status: 'active' as const,
    logo: '/images/restaurant-logo.jpg',
    branches: [
      {
        id: '1',
        name: 'Downtown Branch',
        slug: 'downtown',
        address: '123 Main Street, Downtown',
        status: 'active'
      },
      {
        id: '2',
        name: 'Uptown Branch',
        slug: 'uptown',
        address: '456 Oak Avenue, Uptown',
        status: 'active'
      }
    ],
    createdAt: '2024-01-15T00:00:00Z'
  };

  // Use mock data for now since the API might not be ready
  const restaurantData = mockRestaurant;

  if (isLoading) {
    return <AppLoading />;
  }

  return (
    <div className="min-h-screen bg-[#fbfaf9] p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-4">
            <Link href="/app/restaurant">
              <Button
                variant="ghost"
                size="sm"
                className="text-[#8a745c] hover:text-[#181510] hover:bg-[#f1edea]"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                {MESSAGES.ACTION.BACK}
              </Button>
            </Link>

            <div className="h-6 w-px bg-[#e5e1dc]" />

            <nav className="flex items-center space-x-2 text-sm text-[#8a745c]">
              <Link href="/app/restaurant" className="hover:text-[#181510]">
                Restaurants
              </Link>
              <span>/</span>
              <span className="text-[#181510] font-medium">{restaurantData.name}</span>
            </nav>
          </div>

          {/* Restaurant Header Card */}
          <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-4">
                  {restaurantData.logo && (
                    <div className="w-16 h-16 rounded-lg overflow-hidden bg-[#f1edea]">
                      <img
                        src={restaurantData.logo}
                        alt={restaurantData.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = '/images/restaurant-placeholder.jpg';
                        }}
                      />
                    </div>
                  )}
                  <div>
                    <div className="flex items-center gap-3 mb-2">
                      <h1 className="text-2xl font-bold text-[#181510]">{restaurantData.name}</h1>
                      <Badge className="bg-green-100 text-green-800 border-green-200">
                        {MESSAGES.STATUS.ACTIVE}
                      </Badge>
                    </div>
                    <p className="text-[#8a745c] text-base max-w-2xl">
                      {restaurantData.description}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Link href={`/app/restaurant/${slugShop}/settings`}>
                    <Button
                      variant="outline"
                      className="border-[#e5e1dc] text-[#181510] hover:bg-[#f1edea]"
                    >
                      <Settings className="h-4 w-4 mr-2" />
                      {MESSAGES.NAVIGATION.SETTINGS}
                    </Button>
                  </Link>
                </div>
              </div>
            </CardHeader>
          </Card>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardContent className="p-6 text-center">
              <Store className="h-8 w-8 text-[#8a745c] mx-auto mb-2" />
              <div className="text-2xl font-bold text-[#181510]">{restaurantData.branches?.length || 0}</div>
              <div className="text-sm text-[#8a745c]">Branches</div>
            </CardContent>
          </Card>

          <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardContent className="p-6 text-center">
              <Users className="h-8 w-8 text-[#8a745c] mx-auto mb-2" />
              <div className="text-2xl font-bold text-[#181510]">24</div>
              <div className="text-sm text-[#8a745c]">Staff Members</div>
            </CardContent>
          </Card>

          <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardContent className="p-6 text-center">
              <TrendingUp className="h-8 w-8 text-[#8a745c] mx-auto mb-2" />
              <div className="text-2xl font-bold text-[#181510]">$12.5K</div>
              <div className="text-sm text-[#8a745c]">Monthly Revenue</div>
            </CardContent>
          </Card>

          <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardContent className="p-6 text-center">
              <Calendar className="h-8 w-8 text-[#8a745c] mx-auto mb-2" />
              <div className="text-2xl font-bold text-[#181510]">156</div>
              <div className="text-sm text-[#8a745c]">Reservations</div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Restaurant Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Contact Information */}
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="text-[#181510] flex items-center gap-2">
                  <Phone className="h-5 w-5 text-[#8a745c]" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {restaurantData.address && (
                  <div className="flex items-center gap-3">
                    <MapPin className="h-4 w-4 text-[#8a745c] flex-shrink-0" />
                    <span className="text-[#181510]">
                      {restaurantData.address}
                      {restaurantData.city && `, ${restaurantData.city}`}
                      {restaurantData.country && `, ${restaurantData.country}`}
                    </span>
                  </div>
                )}

                {restaurantData.phone && (
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-[#8a745c] flex-shrink-0" />
                    <span className="text-[#181510]">{restaurantData.phone}</span>
                  </div>
                )}

                {restaurantData.email && (
                  <div className="flex items-center gap-3">
                    <Mail className="h-4 w-4 text-[#8a745c] flex-shrink-0" />
                    <span className="text-[#181510]">{restaurantData.email}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Branches */}
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-[#181510] flex items-center gap-2">
                    <Store className="h-5 w-5 text-[#8a745c]" />
                    Branches ({restaurantData.branches?.length || 0})
                  </CardTitle>
                  <Button
                    size="sm"
                    className="bg-[#8a745c] hover:bg-[#6d5a48] text-white"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Branch
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {restaurantData.branches?.map((branch) => (
                    <Link
                      key={branch.id}
                      href={`/app/restaurant/${slugShop}/${branch.slug}`}
                      className="flex items-center justify-between p-4 bg-[#f9f7f4] rounded-lg hover:bg-[#f1edea] transition-colors group"
                    >
                      <div>
                        <h4 className="font-medium text-[#181510] group-hover:text-[#8a745c]">
                          {branch.name}
                        </h4>
                        <p className="text-sm text-[#8a745c]">{branch.address}</p>
                      </div>
                      <ArrowRight className="h-4 w-4 text-[#8a745c] opacity-0 group-hover:opacity-100 transition-opacity" />
                    </Link>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="space-y-6">
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="text-[#181510]">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link href={`/app/restaurant/${slugShop}/menu`}>
                  <Button
                    variant="outline"
                    className="w-full justify-start border-[#e5e1dc] text-[#181510] hover:bg-[#f1edea]"
                  >
                    <Globe className="h-4 w-4 mr-2" />
                    Manage Menu
                  </Button>
                </Link>

                <Link href={`/app/restaurant/${slugShop}/reservations`}>
                  <Button
                    variant="outline"
                    className="w-full justify-start border-[#e5e1dc] text-[#181510] hover:bg-[#f1edea]"
                  >
                    <Calendar className="h-4 w-4 mr-2" />
                    View Reservations
                  </Button>
                </Link>

                <Link href={`/app/restaurant/${slugShop}/staff`}>
                  <Button
                    variant="outline"
                    className="w-full justify-start border-[#e5e1dc] text-[#181510] hover:bg-[#f1edea]"
                  >
                    <Users className="h-4 w-4 mr-2" />
                    Manage Staff
                  </Button>
                </Link>

                <Link href={`/app/restaurant/${slugShop}/reports`}>
                  <Button
                    variant="outline"
                    className="w-full justify-start border-[#e5e1dc] text-[#181510] hover:bg-[#f1edea]"
                  >
                    <TrendingUp className="h-4 w-4 mr-2" />
                    View Reports
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="text-[#181510]">Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-[#181510]">New reservation for 4 people</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-[#181510]">Menu item updated</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="text-[#181510]">Staff member added</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
