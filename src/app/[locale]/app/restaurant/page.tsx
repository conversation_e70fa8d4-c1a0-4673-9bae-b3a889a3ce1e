'use client';

import { useRouter } from '@/i18n/navigation';
import { RestaurantList } from '@/components/restaurant/RestaurantList';
import { useRestaurants } from '@/hooks/useRestaurant';
import { MESSAGES } from '@/lib/constants/messages';
import { AppLoading } from '@/components/ui/app-loading';

export default function RestaurantShopsPage() {
  const router = useRouter();

  const {
    restaurants,
    isLoading,
    totalCount,
    statusCounts,
    cityCounts,
    filters,
    updateFilters,
    deleteRestaurant,
  } = useRestaurants({
    page: 1,
    limit: 12
  });

  const handleCreateRestaurant = () => {
    router.push('/app/restaurant/new');
  };

  const handleViewRestaurant = (restaurant: any) => {
    router.push(`/app/restaurant/${restaurant.slug}`);
  };

  const handleEditRestaurant = (restaurant: any) => {
    router.push(`/app/restaurant/${restaurant.slug}/settings`);
  };

  const handleDeleteRestaurant = async (restaurant: any) => {
    if (confirm(MESSAGES.WARNING.DELETE_CONFIRMATION)) {
      await deleteRestaurant(restaurant.id);
    }
  };

  if (isLoading) {
    return <AppLoading />;
  }

  return (
    <div className="min-h-screen bg-[#fbfaf9] p-6">
      <RestaurantList
        restaurants={restaurants}
        isLoading={isLoading}
        totalCount={totalCount}
        statusCounts={statusCounts}
        cityCounts={cityCounts}
        filters={filters}
        onFiltersChange={updateFilters}
        onRestaurantView={handleViewRestaurant}
        onRestaurantEdit={handleEditRestaurant}
        onRestaurantDelete={handleDeleteRestaurant}
        onRestaurantCreate={handleCreateRestaurant}
        showFilters={true}
        showCreateButton={true}
      />
    </div>
  );
}
