/**
 * API route for reviews
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const { searchParams } = new URL(request.url);
    
    // Forward query parameters to backend
    const queryString = searchParams.toString();
    const url = `/merchants/${merchantId}/branches/${branchId}/reviews${queryString ? `?${queryString}` : ''}`;
    
    const response = await fetchClient(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching reviews:', error);
    
    // Return mock data for development
    const mockData = {
      data: [
        {
          id: '1',
          customerId: 'customer-1',
          customerName: '<PERSON>e',
          customerEmail: '<EMAIL>',
          customerAvatar: '/images/customers/john-doe.jpg',
          orderId: 'order-1',
          rating: 5,
          title: 'Excellent food and service!',
          comment: 'Had an amazing dinner here. The chicken sandwich was perfectly cooked and the service was outstanding. Will definitely come back!',
          photos: ['/images/reviews/review-1-photo-1.jpg'],
          source: 'google',
          status: 'approved',
          isVerified: true,
          isPublic: true,
          response: {
            message: 'Thank you so much for your kind words! We\'re thrilled you enjoyed your meal.',
            respondedBy: 'Manager',
            respondedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
          },
          tags: ['food', 'service'],
          sentiment: 'positive',
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '2',
          customerId: 'customer-2',
          customerName: 'Jane Smith',
          customerEmail: '<EMAIL>',
          customerAvatar: '/images/customers/jane-smith.jpg',
          rating: 4,
          title: 'Good food, slow service',
          comment: 'The food was delicious but we had to wait quite a while for our order. The staff was friendly though.',
          source: 'yelp',
          status: 'approved',
          isVerified: true,
          isPublic: true,
          tags: ['food', 'service', 'wait time'],
          sentiment: 'neutral',
          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '3',
          customerId: 'customer-3',
          customerName: 'Mike Johnson',
          customerEmail: '<EMAIL>',
          rating: 3,
          title: 'Average experience',
          comment: 'Food was okay, nothing special. Service was average. Might try again in the future.',
          source: 'facebook',
          status: 'pending',
          isVerified: false,
          isPublic: false,
          tags: ['food', 'service'],
          sentiment: 'neutral',
          createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '4',
          customerId: 'customer-4',
          customerName: 'Sarah Wilson',
          customerEmail: '<EMAIL>',
          customerAvatar: '/images/customers/sarah-wilson.jpg',
          rating: 2,
          title: 'Disappointing visit',
          comment: 'Food was cold when it arrived and the server seemed disinterested. Expected better based on the reviews.',
          source: 'tripadvisor',
          status: 'approved',
          isVerified: true,
          isPublic: true,
          response: {
            message: 'We sincerely apologize for your disappointing experience. We\'d like to make this right - please contact us directly.',
            respondedBy: 'Manager',
            respondedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
          },
          tags: ['food', 'service', 'temperature'],
          sentiment: 'negative',
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '5',
          customerId: 'customer-5',
          customerName: 'David Brown',
          customerEmail: '<EMAIL>',
          rating: 5,
          title: 'Perfect date night spot!',
          comment: 'My wife and I had a wonderful anniversary dinner here. The ambiance was perfect and the food was exceptional.',
          photos: ['/images/reviews/review-5-photo-1.jpg', '/images/reviews/review-5-photo-2.jpg'],
          source: 'internal',
          status: 'approved',
          isVerified: true,
          isPublic: true,
          tags: ['ambiance', 'food', 'date night'],
          sentiment: 'positive',
          createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
        }
      ],
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 5,
        itemsPerPage: 20,
        hasNextPage: false,
        hasPreviousPage: false
      }
    };
    
    return NextResponse.json(mockData);
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ merchantId: string; branchId: string }> }
) {
  try {
    const { merchantId, branchId } = await params;
    const body = await request.json();
    
    const response = await fetchClient(`/merchants/${merchantId}/branches/${branchId}/reviews`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error creating review:', error);
    
    // Return mock created review for development
    const mockReview = {
      id: `review-${Date.now()}`,
      customerName: 'New Customer',
      customerEmail: '<EMAIL>',
      rating: 5,
      title: 'Great experience!',
      comment: 'Had a wonderful time at this restaurant.',
      source: 'internal',
      status: 'pending',
      isVerified: false,
      isPublic: false,
      sentiment: 'positive',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    return NextResponse.json(mockReview);
  }
}
