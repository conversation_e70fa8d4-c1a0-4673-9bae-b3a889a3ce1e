package services

import (
	"context"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// AuthService handles authentication business logic
type AuthService struct {
	userRepo  repositories.UserRepository
	secretKey string
	expiresIn time.Duration
}

// NewAuthService creates a new auth service
func NewAuthService(userRepo repositories.UserRepository, secretKey string, expiresIn time.Duration) *AuthService {
	return &AuthService{
		userRepo:  userRepo,
		secretKey: secretKey,
		expiresIn: expiresIn,
	}
}

// LoginRequest represents a login request
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse represents a login response
type LoginResponse struct {
	Token        string       `json:"token"`
	RefreshToken string       `json:"refresh_token"`
	User         *models.User `json:"user"`
	ExpiresAt    time.Time    `json:"expires_at"`
}

// RegisterRequest represents a registration request
type RegisterRequest struct {
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=6"`
	FirstName string `json:"first_name" binding:"required"`
	LastName  string `json:"last_name" binding:"required"`
	Phone     string `json:"phone"`
}

// RegisterResponse represents a registration response
type RegisterResponse struct {
	User    *models.User `json:"user"`
	Message string       `json:"message"`
}

// RefreshTokenRequest represents a refresh token request
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// Claims represents JWT claims
type Claims struct {
	UserID      uuid.UUID `json:"user_id"`
	MerchantID  uuid.UUID `json:"merchant_id"`
	BranchID    uuid.UUID `json:"branch_id"`
	Role        string    `json:"role"`
	Permissions []string  `json:"permissions"`
	jwt.RegisteredClaims
}

// Login authenticates a user and returns a JWT token
func (s *AuthService) Login(ctx context.Context, req LoginRequest) (*LoginResponse, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Find user by email
	// 2. Verify password
	// 3. Generate JWT token
	// 4. Return response

	// For now, return a mock response
	user := &models.User{
		BaseModel: models.BaseModel{ID: uuid.New()},
		Email:     req.Email,
		FirstName: "Demo",
		LastName:  "User",
	}

	token, err := s.generateToken(user)
	if err != nil {
		return nil, err
	}

	return &LoginResponse{
		Token:     token,
		User:      user,
		ExpiresAt: time.Now().Add(s.expiresIn),
	}, nil
}

// Register creates a new user account
func (s *AuthService) Register(ctx context.Context, req RegisterRequest) (*RegisterResponse, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Check if user already exists
	// 2. Hash password
	// 3. Create user in database
	// 4. Return response

	user := &models.User{
		BaseModel: models.BaseModel{ID: uuid.New()},
		Email:     req.Email,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Phone:     req.Phone,
	}

	return &RegisterResponse{
		User:    user,
		Message: "User registered successfully",
	}, nil
}

// RefreshToken refreshes an expired JWT token
func (s *AuthService) RefreshToken(ctx context.Context, req RefreshTokenRequest) (*LoginResponse, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Validate refresh token
	// 2. Get user from refresh token
	// 3. Generate new JWT token
	// 4. Return response

	return &LoginResponse{
		Token:     "new-jwt-token",
		ExpiresAt: time.Now().Add(s.expiresIn),
	}, nil
}

// GetCurrentUser returns the current authenticated user
func (s *AuthService) GetCurrentUser(ctx context.Context, userID interface{}) (*models.User, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Get user ID from context
	// 2. Fetch user from database
	// 3. Return user

	uid, ok := userID.(uuid.UUID)
	if !ok {
		return nil, NewServiceError("INVALID_USER_ID", "Invalid user ID")
	}

	user := &models.User{
		BaseModel: models.BaseModel{ID: uid},
		Email:     "<EMAIL>",
		FirstName: "Demo",
		LastName:  "User",
	}

	return user, nil
}

// generateToken generates a JWT token for a user
func (s *AuthService) generateToken(user *models.User) (string, error) {
	claims := &Claims{
		UserID:     user.ID,
		MerchantID: user.MerchantID,
		BranchID:   *user.BranchID,
		Role:       user.Role.Name,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(s.expiresIn)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "restaurant-api",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.secretKey))
}
