package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// Claims represents the JWT claims
type Claims struct {
	UserID     uuid.UUID `json:"user_id"`
	MerchantID uuid.UUID `json:"merchant_id"`
	BranchID   uuid.UUID `json:"branch_id"`
	Role       string    `json:"role"`
	Permissions []string `json:"permissions"`
	jwt.RegisteredClaims
}

// AuthRequired middleware validates JWT tokens
func AuthRequired(secretKey string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		// Check if header starts with "Bearer "
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.<PERSON><PERSON><PERSON>(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
			c.Abort()
			return
		}

		// Extract token
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Token required"})
			c.Abort()
			return
		}

		// Parse and validate token
		token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
			// Validate signing method
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, jwt.ErrSignatureInvalid
			}
			return []byte(secretKey), nil
		})

		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		// Check if token is valid
		if !token.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		// Extract claims
		claims, ok := token.Claims.(*Claims)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token claims"})
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user_id", claims.UserID)
		c.Set("merchant_id", claims.MerchantID)
		c.Set("branch_id", claims.BranchID)
		c.Set("role", claims.Role)
		c.Set("permissions", claims.Permissions)
		c.Set("claims", claims)

		c.Next()
	}
}

// RequirePermission middleware checks if user has required permission
func RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		permissions, exists := c.Get("permissions")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{"error": "No permissions found"})
			c.Abort()
			return
		}

		userPermissions, ok := permissions.([]string)
		if !ok {
			c.JSON(http.StatusForbidden, gin.H{"error": "Invalid permissions format"})
			c.Abort()
			return
		}

		// Check if user has the required permission
		hasPermission := false
		for _, p := range userPermissions {
			if p == permission {
				hasPermission = true
				break
			}
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAnyPermission middleware checks if user has any of the required permissions
func RequireAnyPermission(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userPermissions, exists := c.Get("permissions")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{"error": "No permissions found"})
			c.Abort()
			return
		}

		userPerms, ok := userPermissions.([]string)
		if !ok {
			c.JSON(http.StatusForbidden, gin.H{"error": "Invalid permissions format"})
			c.Abort()
			return
		}

		// Check if user has any of the required permissions
		hasPermission := false
		for _, requiredPerm := range permissions {
			for _, userPerm := range userPerms {
				if userPerm == requiredPerm {
					hasPermission = true
					break
				}
			}
			if hasPermission {
				break
			}
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireRole middleware checks if user has required role
func RequireRole(role string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("role")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{"error": "No role found"})
			c.Abort()
			return
		}

		if userRole != role {
			c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient role"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// ValidateMerchantAccess middleware ensures user can only access their merchant's data
func ValidateMerchantAccess() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get merchant ID from URL params
		merchantIDParam := c.Param("merchantId")
		if merchantIDParam == "" {
			c.Next()
			return
		}

		merchantIDFromURL, err := uuid.Parse(merchantIDParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
			c.Abort()
			return
		}

		// Get user's merchant ID from token
		userMerchantID, exists := c.Get("merchant_id")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{"error": "No merchant access"})
			c.Abort()
			return
		}

		userMerchantUUID, ok := userMerchantID.(uuid.UUID)
		if !ok {
			c.JSON(http.StatusForbidden, gin.H{"error": "Invalid merchant access"})
			c.Abort()
			return
		}

		// Check if user can access this merchant
		if userMerchantUUID != merchantIDFromURL {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to this merchant"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// ValidateBranchAccess middleware ensures user can only access their branch's data
func ValidateBranchAccess() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get branch ID from URL params
		branchIDParam := c.Param("branchId")
		if branchIDParam == "" {
			c.Next()
			return
		}

		branchIDFromURL, err := uuid.Parse(branchIDParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
			c.Abort()
			return
		}

		// Get user's branch ID from token
		userBranchID, exists := c.Get("branch_id")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{"error": "No branch access"})
			c.Abort()
			return
		}

		userBranchUUID, ok := userBranchID.(uuid.UUID)
		if !ok {
			c.JSON(http.StatusForbidden, gin.H{"error": "Invalid branch access"})
			c.Abort()
			return
		}

		// Check if user can access this branch
		if userBranchUUID != branchIDFromURL {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to this branch"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// GetUserID helper function to get user ID from context
func GetUserID(c *gin.Context) (uuid.UUID, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return uuid.Nil, false
	}

	userUUID, ok := userID.(uuid.UUID)
	return userUUID, ok
}

// GetMerchantID helper function to get merchant ID from context
func GetMerchantID(c *gin.Context) (uuid.UUID, bool) {
	merchantID, exists := c.Get("merchant_id")
	if !exists {
		return uuid.Nil, false
	}

	merchantUUID, ok := merchantID.(uuid.UUID)
	return merchantUUID, ok
}

// GetBranchID helper function to get branch ID from context
func GetBranchID(c *gin.Context) (uuid.UUID, bool) {
	branchID, exists := c.Get("branch_id")
	if !exists {
		return uuid.Nil, false
	}

	branchUUID, ok := branchID.(uuid.UUID)
	return branchUUID, ok
}

// GetUserPermissions helper function to get user permissions from context
func GetUserPermissions(c *gin.Context) ([]string, bool) {
	permissions, exists := c.Get("permissions")
	if !exists {
		return nil, false
	}

	userPermissions, ok := permissions.([]string)
	return userPermissions, ok
}

// HasPermission helper function to check if user has a specific permission
func HasPermission(c *gin.Context, permission string) bool {
	permissions, ok := GetUserPermissions(c)
	if !ok {
		return false
	}

	for _, p := range permissions {
		if p == permission {
			return true
		}
	}
	return false
}
