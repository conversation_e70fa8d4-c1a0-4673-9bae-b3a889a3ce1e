package routes

import (
	"net/http"
	"time"

	"restaurant-backend/internal/api/handlers"
	"restaurant-backend/internal/api/middleware"
	"restaurant-backend/internal/config"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/services"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/gorm"
)

// SetupRoutes configures all routes for the application
func SetupRoutes(router *gin.Engine, db *gorm.DB, cfg *config.Config, logger *logrus.Logger) {
	// Initialize repositories
	merchantRepo := repositories.NewMerchantRepository(db)
	branchRepo := repositories.NewBranchRepository(db)
	userRepo := repositories.NewUserRepository(db)
	roleRepo := repositories.NewRoleRepository(db)
	menuRepo := repositories.NewMenuRepository(db)
	orderRepo := repositories.NewOrderRepository(db)
	reservationRepo := repositories.NewReservationRepository(db)
	tableRepo := repositories.NewTableRepository(db)
	reviewRepo := repositories.NewReviewRepository(db)

	// Initialize services
	authService := services.NewAuthService(userRepo, cfg.JWT.Secret, cfg.JWT.ExpiresIn)
	merchantService := services.NewMerchantService(merchantRepo, logger)
	branchService := services.NewBranchService(branchRepo, logger)
	userService := services.NewUserService(userRepo, roleRepo, logger)
	menuService := services.NewMenuService(menuRepo, logger)
	orderService := services.NewOrderService(orderRepo, logger)
	reservationService := services.NewReservationService(reservationRepo, logger)
	tableService := services.NewTableService(tableRepo, logger)
	reviewService := services.NewReviewService(reviewRepo, logger)

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService, logger)
	merchantHandler := handlers.NewMerchantHandler(merchantService, logger)
	branchHandler := handlers.NewBranchHandler(branchService, logger)
	userHandler := handlers.NewUserHandler(userService, logger)
	menuHandler := handlers.NewMenuHandler(menuService, logger)
	orderHandler := handlers.NewOrderHandler(orderService, logger)
	reservationHandler := handlers.NewReservationHandler(reservationService, logger)
	tableHandler := handlers.NewTableHandler(tableService, logger)
	reviewHandler := handlers.NewReviewHandler(reviewService, logger)
	healthHandler := handlers.NewHealthHandler(db, logger)

	// Setup middleware
	setupMiddleware(router, cfg, logger)

	// Setup API routes
	setupAPIRoutes(router, cfg, logger,
		authHandler,
		merchantHandler,
		branchHandler,
		userHandler,
		menuHandler,
		orderHandler,
		reservationHandler,
		tableHandler,
		reviewHandler,
		healthHandler,
	)

	// Setup documentation
	setupDocumentation(router)
}

// setupMiddleware configures global middleware
func setupMiddleware(router *gin.Engine, cfg *config.Config, logger *logrus.Logger) {
	// Recovery middleware
	router.Use(gin.Recovery())

	// CORS middleware
	router.Use(cors.New(cors.Config{
		AllowOrigins:     cfg.CORS.AllowedOrigins,
		AllowMethods:     cfg.CORS.AllowedMethods,
		AllowHeaders:     cfg.CORS.AllowedHeaders,
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	// Security headers middleware
	router.Use(middleware.SecurityHeaders())

	// Request logging middleware
	router.Use(middleware.RequestLogger(logger))

	// Rate limiting middleware
	if cfg.Rate.Requests > 0 {
		router.Use(middleware.RateLimit(cfg.Rate.Requests, cfg.Rate.Window))
	}
}

// setupAPIRoutes configures all API routes
func setupAPIRoutes(
	router *gin.Engine,
	cfg *config.Config,
	logger *logrus.Logger,
	authHandler *handlers.AuthHandler,
	merchantHandler *handlers.MerchantHandler,
	branchHandler *handlers.BranchHandler,
	userHandler *handlers.UserHandler,
	menuHandler *handlers.MenuHandler,
	orderHandler *handlers.OrderHandler,
	reservationHandler *handlers.ReservationHandler,
	tableHandler *handlers.TableHandler,
	reviewHandler *handlers.ReviewHandler,
	healthHandler *handlers.HealthHandler,
) {
	// Health check routes (no auth required)
	router.GET("/health", healthHandler.Health)
	router.GET("/ready", healthHandler.Ready)

	// API v1 routes
	v1 := router.Group("/api/v1")

	// Authentication routes (no auth required)
	auth := v1.Group("/auth")
	{
		auth.POST("/login", authHandler.Login)
		auth.POST("/register", authHandler.Register)
		auth.POST("/refresh", authHandler.RefreshToken)
		auth.POST("/logout", middleware.AuthRequired(cfg.JWT.Secret), authHandler.Logout)
		auth.GET("/me", middleware.AuthRequired(cfg.JWT.Secret), authHandler.GetCurrentUser)
	}

	// Merchant routes
	merchants := v1.Group("/merchants")
	merchants.Use(middleware.AuthRequired(cfg.JWT.Secret))
	{
		merchants.GET("", merchantHandler.GetMerchants)
		merchants.POST("", merchantHandler.CreateMerchant)
		merchants.GET("/:merchantId", merchantHandler.GetMerchant)
		merchants.PUT("/:merchantId", merchantHandler.UpdateMerchant)
		merchants.DELETE("/:merchantId", merchantHandler.DeleteMerchant)

		// Branch routes
		branches := merchants.Group("/:merchantId/branches")
		{
			branches.GET("", branchHandler.GetBranches)
			branches.POST("", branchHandler.CreateBranch)
			branches.GET("/:branchId", branchHandler.GetBranch)
			branches.PUT("/:branchId", branchHandler.UpdateBranch)
			branches.DELETE("/:branchId", branchHandler.DeleteBranch)

			// Branch-specific routes
			branch := branches.Group("/:branchId")
			{
				// Staff/User routes
				staff := branch.Group("/staff")
				{
					staff.GET("", userHandler.GetUsers)
					staff.POST("", userHandler.CreateUser)
					staff.GET("/:userId", userHandler.GetUser)
					staff.PUT("/:userId", userHandler.UpdateUser)
					staff.DELETE("/:userId", userHandler.DeleteUser)
					staff.PATCH("/:userId/status", userHandler.UpdateUserStatus)
					staff.GET("/roles", userHandler.GetRoles)
					staff.POST("/roles", userHandler.CreateRole)
					staff.PUT("/roles/:roleId", userHandler.UpdateRole)
					staff.DELETE("/roles/:roleId", userHandler.DeleteRole)
				}

				// Menu routes
				menu := branch.Group("/menu")
				{
					// Categories
					menu.GET("/categories", menuHandler.GetCategories)
					menu.POST("/categories", menuHandler.CreateCategory)
					menu.PUT("/categories/:categoryId", menuHandler.UpdateCategory)
					menu.DELETE("/categories/:categoryId", menuHandler.DeleteCategory)

					// Items
					menu.GET("/items", menuHandler.GetMenuItems)
					menu.POST("/items", menuHandler.CreateMenuItem)
					menu.GET("/items/:itemId", menuHandler.GetMenuItem)
					menu.PUT("/items/:itemId", menuHandler.UpdateMenuItem)
					menu.DELETE("/items/:itemId", menuHandler.DeleteMenuItem)
					menu.PATCH("/items/:itemId/availability", menuHandler.ToggleAvailability)
				}

				// Order routes
				orders := branch.Group("/orders")
				{
					orders.GET("", orderHandler.GetOrders)
					orders.POST("", orderHandler.CreateOrder)
					orders.GET("/:orderId", orderHandler.GetOrder)
					orders.PUT("/:orderId", orderHandler.UpdateOrder)
					orders.PATCH("/:orderId/status", orderHandler.UpdateOrderStatus)
					orders.DELETE("/:orderId", orderHandler.CancelOrder)
					orders.GET("/active", orderHandler.GetActiveOrders)
					orders.GET("/completed", orderHandler.GetCompletedOrders)
				}

				// Reservation routes
				reservations := branch.Group("/reservations")
				{
					reservations.GET("", reservationHandler.GetReservations)
					reservations.POST("", reservationHandler.CreateReservation)
					reservations.GET("/:reservationId", reservationHandler.GetReservation)
					reservations.PUT("/:reservationId", reservationHandler.UpdateReservation)
					reservations.DELETE("/:reservationId", reservationHandler.CancelReservation)
					reservations.POST("/:reservationId/checkin", reservationHandler.CheckInReservation)
					reservations.POST("/:reservationId/no-show", reservationHandler.MarkNoShow)
					reservations.GET("/today", reservationHandler.GetTodayReservations)
					reservations.GET("/availability", reservationHandler.GetAvailability)
				}

				// Table routes
				tables := branch.Group("/tables")
				{
					tables.GET("", tableHandler.GetTables)
					tables.POST("", tableHandler.CreateTable)
					tables.GET("/:tableId", tableHandler.GetTable)
					tables.PUT("/:tableId", tableHandler.UpdateTable)
					tables.DELETE("/:tableId", tableHandler.DeleteTable)
					tables.PATCH("/:tableId/status", tableHandler.UpdateTableStatus)
					tables.GET("/layout", tableHandler.GetLayout)
					tables.PUT("/layout", tableHandler.UpdateLayout)
					tables.GET("/areas", tableHandler.GetAreas)
					tables.POST("/areas", tableHandler.CreateArea)
					tables.PUT("/areas/:areaId", tableHandler.UpdateArea)
					tables.DELETE("/areas/:areaId", tableHandler.DeleteArea)
					tables.POST("/:tableId/qr-code", tableHandler.GenerateQRCode)
				}

				// Review routes
				reviews := branch.Group("/reviews")
				{
					reviews.GET("", reviewHandler.GetReviews)
					reviews.GET("/:reviewId", reviewHandler.GetReview)
					reviews.POST("/:reviewId/respond", reviewHandler.RespondToReview)
					reviews.PUT("/:reviewId/respond", reviewHandler.UpdateResponse)
					reviews.DELETE("/:reviewId/respond", reviewHandler.DeleteResponse)
					reviews.PATCH("/:reviewId/status", reviewHandler.UpdateReviewStatus)
					reviews.GET("/stats", reviewHandler.GetReviewStats)
					reviews.GET("/recent", reviewHandler.GetRecentReviews)
					reviews.GET("/pending", reviewHandler.GetPendingReviews)
				}

				// Reports routes
				reports := branch.Group("/reports")
				{
					reports.GET("/sales-trends", orderHandler.GetSalesTrends)
					reports.GET("/popular-items", menuHandler.GetPopularItems)
					reports.GET("/revenue", orderHandler.GetRevenueReport)
				}

				// Dashboard routes
				dashboard := branch.Group("/dashboard")
				{
					dashboard.GET("/stats", orderHandler.GetDashboardStats)
					dashboard.GET("/recent-activity", orderHandler.GetRecentActivity)
				}
			}
		}
	}

	// Metrics endpoint (if enabled)
	if cfg.Metrics.Enabled {
		router.GET(cfg.Metrics.Path, gin.WrapH(middleware.PrometheusHandler()))
	}
}

// setupDocumentation configures API documentation
func setupDocumentation(router *gin.Engine) {
	// Swagger documentation
	router.GET("/docs/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	
	// Redirect root to docs
	router.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/docs/index.html")
	})
}
